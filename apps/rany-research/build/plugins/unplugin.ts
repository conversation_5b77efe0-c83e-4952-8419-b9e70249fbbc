import process from 'node:process';
import path from 'node:path';
import type { PluginOption } from 'vite';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';

import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import { FileSystemIconLoader } from 'unplugin-icons/loaders';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import svgLoader from 'vite-svg-loader';

export function setupUnplugin(viteEnv: Env.ImportMeta) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;

  const localIconPath = path.join(process.cwd(), 'src/assets/svg-icon');
  // 添加 icon-library 路径
  const iconLibraryPath = path.join(process.cwd(), '../../packages/icon-library/src/icons');
  const iconLibraryColorPath = path.join(process.cwd(), '../../packages/icon-library/src/icons-color');

  /** The name of the local icon collection */
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, '');

  const plugins: PluginOption[] = [
    // AutoImport 插件 - 自动导入图标组件
    AutoImport({
      imports: ['vue', 'pinia', '@vueuse/core'],
      dts: 'src/typings/auto-imports.d.ts',
      dirs: ['src/composables', 'src/stores', 'src/store', 'src/components/**/*.vue'],
      resolvers: [
        // 只自动导入图标组件，避免与其他组件冲突
        IconsResolver({
          componentPrefix: 'Icon',
          enabledCollections: ['ry'],
          customCollections: ['ry', 'ry-color']
        })
      ],
      vueTemplate: true
    }),

    Icons({
      compiler: 'vue3',
      customCollections: {
        [collectionName]: FileSystemIconLoader(localIconPath, svg =>
          svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        ),
        // 添加 icon-library 中的图标支持
        ry: FileSystemIconLoader(iconLibraryPath, svg =>
          svg.replace(
            /stroke="(?:#[0-9A-Fa-f]{3,6}|white|black|red|blue|green|yellow|gray|grey)"/g,
            'stroke="currentColor"'
          )
        ),
        // 添加 icon-library 中的彩色图标支持
        'ry-color': FileSystemIconLoader(iconLibraryColorPath, svg => svg)
      },
      scale: 1,
      defaultClass: 'inline-block'
    }),
    Components({
      dts: 'src/typings/components.d.ts',
      types: [{ from: 'vue-router', names: ['RouterLink', 'RouterView'] }],
      resolvers: [
        // 添加 NaiveUiResolver 用于组件自动导入
        NaiveUiResolver(),
        // 图标相关的 resolver
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX }),
        // 支持 icon-library 的 ry 图标集合，使用 Icon 前缀
        IconsResolver({
          componentPrefix: 'Icon',
          enabledCollections: ['ry'],
          customCollections: ['ry', 'ry-color']
        })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: 'body-last',
      customDomId: '__SVG_ICON_LOCAL__'
    }),
    svgLoader()
  ];

  return plugins;
}
