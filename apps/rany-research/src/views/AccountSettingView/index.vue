<script setup lang="ts">
import { AccountIndex } from 'component-library';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { getUserInfo, smsCode, updateUserInfo, verifyMobile, wardList } from '@/apis/user';

interface WardItem {
  id: number;
  name: string;
  [key: string]: any;
}

interface UserData {
  userId?: string;
  userName?: string;
  nickName?: string;
  mobile?: string;
  email?: string;
  name?: string;
  avatar?: string;
  role?: string;
  id?: string;
  [key: string]: any;
}

interface ResponseData {
  code: number;
  data?: any;
  message?: string;
  [key: string]: any;
}

const userStore = useUserStore();
const router = useRouter();
const userData = ref(userStore.user || {});
const userRoles = ref([]);
const formData = ref<any>({});
const wardListData = ref<WardItem[]>([]);

const fetchUserInfo = async () => {
  const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
  const response = await getUserInfo(userId);
  if (response && typeof response === 'object' && 'code' in response && response.code === 1 && 'data' in response) {
    formData.value = response.data;
    // 更新
    if (userStore.settings.wardId !== response.data?.wardId) {
      userStore.settings.wardId = response.data?.wardId;
      userStore.settings.wardName = response.data?.wardName;
    }
  }
};

const fetchWardList = async () => {
  try {
    const response = await wardList({
      parentId: 0,
      hospCollId: userStore.user?.hospitalCollectionId
    });
    if (
      response &&
      typeof response === 'object' &&
      'code' in response &&
      response.code === 1 &&
      'data' in response &&
      response.data.records
    ) {
      wardListData.value = response.data.records;
    }
  } catch (error) {
    console.error('Failed to fetch ward list:', error);
  }
};

const updateUserInfoFunc = async (data: any) => {
  const params = {
    oldPassword: data.oldPassword,
    password: data.password,
    wardId: data.wardId,
    email: data.email,
    mobile: data.mobile,
    uptUserId: userStore.user?.userId
  };
  const response = (await updateUserInfo(params)) as ResponseData;
  if (response && typeof response === 'object' && 'code' in response && response.code === 1) {
    fetchUserInfo();
    if (data.name || data.avatar || data.email) {
      // 更新全局用户信息
      const updatedInfo: UserData = { ...userStore.userInfo };
      Object.keys(data).forEach(key => {
        if (Object.hasOwn(updatedInfo, key)) {
          updatedInfo[key] = data[key];
        }
      });
    }
  } else {
    ElMessage.error('更新失败,请联系管理员');
  }
  return response;
};

const updateWard = async (wardData: any) => {
  await updateUserInfoFunc(wardData);
  const updatedInfo: UserData = { ...userStore.userInfo };
  fetchUserInfo();
};

const updatePassword = async (data: any) => {
  const response = (await updateUserInfoFunc(data)) as ResponseData;
  if (response && typeof response === 'object' && 'code' in response && response.code === 1) {
    ElMessage.success('密码修改成功，即将重新登录');
  }
};

const updateMobile = async (data: any) => {
  const response = (await updateUserInfoFunc(data)) as ResponseData;
  if (response && typeof response === 'object' && 'code' in response && response.code === 1) {
    if (data.mobile) {
      const updatedInfo: UserData = {
        ...userStore.userInfo,
        mobile: data.mobile
      };
      userStore.setUserInfo(updatedInfo);
    }
    ElMessage.success('手机号码更新成功');
    fetchUserInfo();
  }
};

const updateEmail = async (data: any) => {
  const response = (await updateUserInfoFunc(data)) as ResponseData;
  if (response && typeof response === 'object' && 'code' in response && response.code === 1) {
    if (data.email) {
      const updatedInfo: UserData = {
        ...userStore.userInfo,
        email: data.email
      };
      userStore.setUserInfo(updatedInfo);
    }
    ElMessage.success('邮箱更新成功');
    fetchUserInfo();
  }
};

const sendSmsCode = async (mobile: string) => {
  await smsCode(mobile);
};

const verifyMobileCode = async (data: any, callback: (isValid: boolean) => void) => {
  const response = (await verifyMobile(data)) as ResponseData;
  if (response && typeof response === 'object' && 'data' in response) {
    callback(response.data === true);
  } else {
    callback(false);
  }
};

const logoutUser = () => {
  router.push('/login');
};

const handleLogout = () => {
  // userStore.logout();
  localStorage.removeItem('user');
  router.push('/login');
};

onMounted(() => {
  // fetchUserInfo();
  fetchWardList();
});
</script>

<template>
  <div class="h-full w-full">
    <AccountIndex
      :user-data="userData"
      :user-roles="userRoles"
      :form-data="formData"
      :ward-list="wardListData"
      @fetch-user-info="fetchUserInfo"
      @update-user-info="updateUserInfoFunc"
      @update-ward="updateWard"
      @update-password="updatePassword"
      @update-mobile="updateMobile"
      @update-email="updateEmail"
      @logout-user="logoutUser"
      @fetch-ward-list="fetchWardList"
      @handle-logout="handleLogout"
    />
  </div>
</template>

<style scoped>
:deep(.ep-button.is-text) {
  --ep-button-text-color: #606266;
}
:deep(.ep-button) {
  font-weight: 400;
}
</style>
