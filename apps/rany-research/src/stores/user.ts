import { defineStore } from 'pinia';
import { ref } from 'vue';

interface UserInfo {
  id: string;
  name: string;
  avatar: string;
  email: string;
  role: string;
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo>({
    id: '',
    name: '',
    avatar: '',
    email: '',
    role: ''
  });

  const token = ref<string>('');

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info;
  };

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  };

  // 登录
  const login = async (username: string, password: string) => {
    try {
      // 这里应该调用实际的登录API
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      if (!response.ok) {
        throw new Error('登录失败');
      }

      const data = await response.json();
      setToken(data.token);
      setUserInfo(data.userInfo);

      return true;
    } catch (error) {
      console.error('登录失败:', error);
      return false;
    }
  };

  // 登出
  const logout = () => {
    userInfo.value = {
      id: '',
      name: '',
      avatar: '',
      email: '',
      role: ''
    };
    token.value = '';
    localStorage.removeItem('token');
  };

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await fetch('/api/user/info', {
        headers: {
          Authorization: `Bearer ${token.value}`
        }
      });

      if (!response.ok) {
        throw new Error('获取用户信息失败');
      }

      const data = await response.json();
      setUserInfo(data);

      return true;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return false;
    }
  };

  return {
    userInfo,
    token,
    login,
    logout,
    setUserInfo,
    setToken,
    getUserInfo
  };
});
