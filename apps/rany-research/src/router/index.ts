import { create<PERSON><PERSON>oryHist<PERSON>, createRouter, createWebHashHistory, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import type { RouteRecordRaw } from 'vue-router';

// 布局组件
import MainLayout from '@/layouts/new-layout/MainLayout.vue';

// 自定义路由

// 404等页面
const NotFound = () => import('@/views/_builtin/404/index.vue');
const Forbidden = () => import('@/views/_builtin/403/index.vue');
const ServerError = () => import('@/views/_builtin/500/index.vue');
const IframePage = () => import('@/views/_builtin/iframe-page/[url].vue');
const Login = () => import('@/views/_builtin/login/index.vue');

// 数据基础
const DataBaseDisease = () => import('@/views/data-base/disease/index.vue');
const DataBasePatient = () => import('@/views/data-base/patient/index.vue');
const DataBasePatientBag = () => import('@/views/data-base/patient-bag/[id].vue');
const DataBaseRegisterDetail = () => import('@/views/data-base/register/detail/[id].vue');
const DataBaseRegisterTable = () => import('@/views/data-base/register/table/index.vue');
const DataBaseRisk = () => import('@/views/data-base/risk/index.vue');
const DataBaseSuite = () => import('@/views/data-base/suite/index.vue');

// 试验管理
const ExperimentDetail = () => import('@/views/experiment/detail/[id].vue');
const ExperimentTable = () => import('@/views/experiment/table/index.vue');

// 流程图
const FlowChart = () => import('@/views/flow-chart/index.vue');

// 医院管理
const HospitalDetail = () => import('@/views/hospital/detail/[id].vue');
const HospitalTable = () => import('@/views/hospital/table/index.vue');

// 方案管理
const ProgramLibrary = () => import('@/views/program/library/index.vue');
const ProgramLibraryDetail = () => import('@/views/program/library-detail/[id].vue');
const ProgramPlan = () => import('@/views/program/plan/index.vue');
const ProgramPlanDetail = () => import('@/views/program/plan-detail/[id].vue');
const ProgramLibraryOperate = () => import('@/views/program/library-operate/index.vue');

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'root',
    redirect: '/home/<USER>',
    meta: {
      title: 'root',
      constant: true
    }
  },
  {
    path: '/home',
    name: 'home',
    redirect: '/home/<USER>',
    component: MainLayout,
    children: [
      {
        path: 'ai-home/:id?',
        name: 'ai-home',
        props: true,
        component: () => import('@/views/HomeView/index.vue'),
        meta: {
          title: 'AI首页',
          hidden: true,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/ai',
    // component: Layout,
    redirect: '/ai/home',
    children: [
      {
        path: 'home',
        name: 'AiHome',
        component: () => import('@/views/HomeView/index.vue'),
        meta: {
          title: '门户',
          requiresAuth: true,
          hidden: true,
          showAside: false,
          topMenu: 'home'
        }
      },
      {
        path: 'chat',
        name: 'AiChat',
        component: () => import('@/views/AiChatView/index.vue'),
        meta: {
          title: 'AI聊天',
          requiresAuth: true,
          hidden: true,
          topMenu: 'home',
          type: 'platform'
        }
      }
    ]
  },
  {
    path: '/account',
    name: 'AccountSetting',
    component: MainLayout,
    meta: { title: '账户设置', requiresAuth: true, type: 'platform' },
    redirect: '/account/index',
    children: [
      {
        path: 'index',
        name: 'AccountSettingIndex',
        component: () => import('@/views/AccountSettingView/index.vue'),
        meta: {
          title: '个人信息设置',
          requiresAuth: false,
          type: 'platform'
        }
      }
    ]
  },
  {
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    name: 'login',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'loginPage',
        component: Login,
        props: true,
        meta: {
          title: 'login',
          constant: true,
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/403',
    name: '403',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'forbidden',
        component: Forbidden,
        meta: {
          title: '403',
          constant: true,
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'notFound',
        component: NotFound,
        meta: {
          title: '404',
          constant: true,
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/500',
    name: '500',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'serverError',
        component: ServerError,
        meta: {
          title: '500',
          constant: true,
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/iframe-page/:url',
    name: 'iframe-page',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'iframePage',
        component: IframePage,
        props: true,
        meta: {
          title: 'iframe-page',
          constant: true,
          hideInMenu: true,
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/data-base',
    name: 'data-base',
    component: MainLayout,
    meta: {
      title: '数据基础',
      icon: 'mdi:database',
      order: 5,
      constant: true,
      type: 'rany-research',
      menu: true
    },
    children: [
      {
        path: 'disease',
        name: 'data-base_disease',
        component: DataBaseDisease,
        meta: {
          title: '疾病分型',
          icon: 'mdi:medical-bag',
          constant: true,
          type: 'rany-research'
        }
      },
      {
        path: 'patient',
        name: 'data-base_patient',
        component: DataBasePatient,
        meta: {
          title: '患者管理',
          icon: 'mdi:account-group',
          constant: true,
          type: 'rany-research'
        }
      },
      {
        path: 'patient-bag/:id',
        name: 'data-base_patient-bag',
        component: DataBasePatientBag,
        meta: {
          title: 'data-base_patient-bag',
          hideInMenu: true,
          hidden: true,
          type: 'rany-research'
        }
      },
      {
        path: 'register',
        name: 'data-base_register',
        redirect: '/data-base/register/table',
        meta: {
          title: '登记首页',
          icon: 'mdi:clipboard-outline',
          constant: true,
          type: 'rany-research'
        },
        children: [
          {
            path: 'detail/:id',
            name: 'data-base_register_detail',
            component: DataBaseRegisterDetail,
            meta: {
              title: '项目组套',
              icon: 'solar:file-text-bold',
              hideInMenu: true,
              activeMenu: 'data-base_register',
              constant: true,
              hidden: true,
              type: 'rany-research'
            }
          },
          {
            path: 'table',
            name: 'data-base_register_table',
            component: DataBaseRegisterTable,
            meta: {
              title: '登记首页',
              icon: 'mdi-light:table',
              hideInMenu: true,
              activeMenu: 'data-base_register',
              constant: true,
              hidden: true,
              type: 'rany-research'
            }
          }
        ]
      },
      {
        path: 'risk',
        name: 'data-base_risk',
        component: DataBaseRisk,
        meta: {
          title: '危险分型',
          icon: 'mdi:shield-check',
          constant: true,
          type: 'rany-research'
        }
      },
      {
        path: 'suite',
        name: 'data-base_suite',
        component: DataBaseSuite,
        meta: {
          title: 'data-base_suite',
          icon: 'solar:documents-bold-duotone',
          constant: true,
          hidden: true,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/experiment',
    name: 'experiment',
    component: MainLayout,
    redirect: '/experiment/table',
    meta: {
      title: '试验管理',
      icon: 'solar:test-tube-bold',
      constant: true,
      order: 2,
      menu: true,
      type: 'rany-research'
    },
    children: [
      {
        path: 'detail/:id',
        name: 'experiment_detail',
        component: ExperimentDetail,
        meta: {
          title: 'experiment_detail',
          icon: 'solar:file-text-bold',
          hideInMenu: true,
          activeMenu: 'experiment',
          constant: true,
          hidden: true,
          type: 'rany-research'
        }
      },
      {
        path: 'table',
        name: 'experiment_table',
        component: ExperimentTable,
        meta: {
          title: '试验管理',
          icon: 'mdi-light:table',
          hideInMenu: true,
          activeMenu: 'experiment',
          constant: true,
          hidden: true,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/flow-chart',
    name: 'flow-chart',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'flowChart',
        component: FlowChart,
        meta: {
          title: '治疗流程图',
          icon: 'mdi:chart-line',
          constant: true,
          order: 1,
          menu: true,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/hospital',
    name: 'hospital',
    component: MainLayout,
    redirect: '/hospital/table',
    meta: {
      title: '医院管理',
      icon: 'icon-park-twotone:hospital',
      order: 4,
      constant: true,
      menu: true,
      type: 'rany-research'
    },
    children: [
      {
        path: 'detail/:id',
        name: 'hospital_detail',
        component: HospitalDetail,
        meta: {
          title: 'hospital_detail',
          icon: 'solar:file-text-bold',
          hideInMenu: true,
          constant: true,
          hidden: true,
          type: 'rany-research'
        }
      },
      {
        path: 'table',
        name: 'hospital_table',
        component: HospitalTable,
        meta: {
          title: 'hospital_table',
          icon: 'mdi-light:table',
          hideInMenu: true,
          constant: true,
          activeMenu: 'hospital',
          hidden: true,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/program',
    name: 'program',
    component: MainLayout,
    meta: {
      title: '方案管理',
      icon: 'mdi:bag-carry-on',
      constant: true,
      order: 3,
      menu: true,
      type: 'rany-research'
    },
    children: [
      {
        path: 'library',
        name: 'program_library',
        component: ProgramLibrary,
        meta: {
          title: '方案库',
          icon: 'mdi:access-point-check',
          type: 'rany-research'
        }
      },
      {
        path: 'library-detail/:id',
        name: 'program_library-detail',
        component: ProgramLibraryDetail,
        meta: {
          title: '方案详情',
          hideInMenu: true,
          constant: true,
          activeMenu: 'program_library',
          hidden: true,
          type: 'rany-research'
        }
      },
      {
        path: 'operate/:action(show|edit)/:id?', // 使用动态参数匹配 show 或 edit，id 可选
        name: 'program_library-operate',
        component: ProgramLibraryOperate,
        meta: {
          title: '方案操作',
          hideInMenu: true,
          activeMenu: 'program_library',
          hidden: true,
          closable: false,
          type: 'rany-research'
        }
      },
      {
        path: 'plan',
        name: 'program_plan',
        component: ProgramPlan,
        meta: {
          title: '诊疗计划',
          icon: 'mdi:swap-vertical-variant',
          type: 'rany-research'
        }
      },
      {
        path: 'plan-detail/:id',
        name: 'program_plan-detail',
        component: ProgramPlanDetail,
        meta: {
          title: '诊疗计划',
          hideInMenu: true,
          constant: true,
          activeMenu: 'program_plan',
          hidden: true,
          type: 'rany-research'
        }
      },
      {
        path: 'plan_operate/:action(show|edit)/:id?', // 使用动态参数匹配 show 或 edit，id 可选
        name: 'program_plan-operate',
        component: FlowChart,
        meta: {
          title: '诊疗计划操作',
          hideInMenu: true,
          activeMenu: 'program_plan',
          hidden: true,
          closable: false,
          type: 'rany-research'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: MainLayout,
    children: [
      {
        path: '',
        name: '404Page',
        component: NotFound,
        meta: {
          title: 'not-found',
          constant: true
        }
      }
    ]
  }
];

const { VITE_ROUTER_HISTORY_MODE = 'history', VITE_BASE_URL } = import.meta.env;

const historyCreatorMap = {
  hash: createWebHashHistory,
  history: createWebHistory,
  memory: createMemoryHistory
} as const;

// 创建路由实例
export const router = createRouter({
  history: historyCreatorMap[VITE_ROUTER_HISTORY_MODE as keyof typeof historyCreatorMap](VITE_BASE_URL),
  routes
});

// 路由守卫
export async function setupRouter(app: App) {
  app.use(router);

  // 全局前置守卫
  router.beforeEach((_to, _from, next) => {
    // 这里可以添加登录验证等逻辑
    next();
  });

  // 全局后置钩子
  router.afterEach((to, _from) => {
    // 处理页面标题等
    if (to.meta.title) {
      document.title = to.meta.title as string;
    }
  });

  await router.isReady();
}
